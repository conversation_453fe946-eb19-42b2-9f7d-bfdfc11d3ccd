package com.buz.reactive.demo

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.actor
import kotlinx.coroutines.channels.produce
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.selects.select
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import org.junit.Test

import org.junit.Assert.*
import kotlin.coroutines.suspendCoroutine

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    var nihao = 0L
    @Test
    fun addition_isCorrect() {
        runBlocking {
            testCoroutine()
            delay(1000)
        }
    }

    fun testCoroutine(){
        GlobalScope.launch(Dispatchers.Default) {
            yield()
            while (true){
                a++
                withContext(Dispatchers.IO) {
                    delay(1)
                }
                println("a:$a,threadId:${Thread.currentThread().id},thread:${Thread.currentThread().name}")
            }
        }
    }
}