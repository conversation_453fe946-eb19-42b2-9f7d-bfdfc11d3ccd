package com.buz.reactive.demo.ui.viewmodel

import androidx.lifecycle.ViewModel
import com.buz.reactive.demo.data.repository.UserRepository
import com.buz.reactive.demo.entity.User
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

sealed interface UserProfileUIState{
    data class Data(val user: User) : UserProfileUIState
    object Loading : UserProfileUIState
}



@HiltViewModel
class UserProfileViewmodel @Inject constructor(

    private val userRepository: UserRepository
) : ViewModel() {

    private val _userState = MutableStateFlow(UserProfileUIState.Loading)
    val userState: StateFlow<UserProfileUIState> = _userState


}